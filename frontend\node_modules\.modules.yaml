hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.3':
    '@babel/core': private
  '@babel/generator@7.28.3':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.3':
    '@babel/helpers': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-jsx': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.3':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@rollup/rollup-win32-x64-msvc@4.48.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/estree@1.0.8':
    '@types/estree': private
  babel-plugin-jsx-dom-expressions@0.40.1(@babel/core@7.28.3):
    babel-plugin-jsx-dom-expressions: private
  babel-preset-solid@1.9.9(@babel/core@7.28.3)(solid-js@1.9.9):
    babel-preset-solid: private
  browserslist@4.25.3:
    browserslist: private
  caniuse-lite@1.0.30001737:
    caniuse-lite: private
  convert-source-map@2.0.0:
    convert-source-map: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  electron-to-chromium@1.5.208:
    electron-to-chromium: private
  entities@6.0.1:
    entities: private
  esbuild@0.25.9:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  gensync@1.0.0-beta.2:
    gensync: private
  html-entities@2.3.3:
    html-entities: private
  is-what@4.1.16:
    is-what: private
  js-tokens@4.0.0:
    js-tokens: private
  jsesc@3.1.0:
    jsesc: private
  json5@2.2.3:
    json5: private
  lru-cache@5.1.1:
    lru-cache: private
  merge-anything@5.1.7:
    merge-anything: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  node-releases@2.0.19:
    node-releases: private
  parse5@7.3.0:
    parse5: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  rollup@4.48.0:
    rollup: private
  semver@6.3.1:
    semver: private
  seroval-plugins@1.3.2(seroval@1.3.2):
    seroval-plugins: private
  seroval@1.3.2:
    seroval: private
  solid-refresh@0.6.3(solid-js@1.9.9):
    solid-refresh: private
  source-map-js@1.2.1:
    source-map-js: private
  tinyglobby@0.2.14:
    tinyglobby: private
  update-browserslist-db@1.1.3(browserslist@4.25.3):
    update-browserslist-db: private
  validate-html-nesting@1.2.3:
    validate-html-nesting: private
  vitefu@1.1.1(vite@7.1.3):
    vitefu: private
  yallist@3.1.1:
    yallist: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Sun, 24 Aug 2025 05:52:01 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-arm64@0.25.9'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.25.9'
  - '@rollup/rollup-android-arm-eabi@4.48.0'
  - '@rollup/rollup-android-arm64@4.48.0'
  - '@rollup/rollup-darwin-arm64@4.48.0'
  - '@rollup/rollup-darwin-x64@4.48.0'
  - '@rollup/rollup-freebsd-arm64@4.48.0'
  - '@rollup/rollup-freebsd-x64@4.48.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.48.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.48.0'
  - '@rollup/rollup-linux-arm64-gnu@4.48.0'
  - '@rollup/rollup-linux-arm64-musl@4.48.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.48.0'
  - '@rollup/rollup-linux-ppc64-gnu@4.48.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.48.0'
  - '@rollup/rollup-linux-riscv64-musl@4.48.0'
  - '@rollup/rollup-linux-s390x-gnu@4.48.0'
  - '@rollup/rollup-linux-x64-gnu@4.48.0'
  - '@rollup/rollup-linux-x64-musl@4.48.0'
  - '@rollup/rollup-win32-arm64-msvc@4.48.0'
  - '@rollup/rollup-win32-ia32-msvc@4.48.0'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\桌面\AI编程\小游戏\Echo Mailbox\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
