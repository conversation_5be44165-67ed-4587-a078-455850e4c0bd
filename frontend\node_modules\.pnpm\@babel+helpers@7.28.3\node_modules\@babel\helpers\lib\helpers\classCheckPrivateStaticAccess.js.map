{"version": 3, "names": ["_assert<PERSON>lassBrand", "require", "_classCheckPrivateStaticAccess", "receiver", "classConstructor", "returnValue", "assertClassBrand"], "sources": ["../../src/helpers/classCheckPrivateStaticAccess.js"], "sourcesContent": ["/* @minVersion 7.13.10 */\n/* @onlyBabel7 */\n\nimport assertClassBrand from \"assertClassBrand\";\nexport default function _classCheckPrivateStaticAccess(\n  receiver,\n  classConstructor,\n  returnValue,\n) {\n  return assertClassBrand(classConstructor, receiver, returnValue);\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,iBAAA,GAAAC,OAAA;AACe,SAASC,8BAA8BA,CACpDC,QAAQ,EACRC,gBAAgB,EAChBC,WAAW,EACX;EACA,OAAOC,iBAAgB,CAACF,gBAAgB,EAAED,QAAQ,EAAEE,WAAW,CAAC;AAClE", "ignoreList": []}