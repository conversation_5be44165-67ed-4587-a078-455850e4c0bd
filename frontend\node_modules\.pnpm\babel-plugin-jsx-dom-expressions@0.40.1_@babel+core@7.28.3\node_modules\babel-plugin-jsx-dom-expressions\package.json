{"name": "babel-plugin-jsx-dom-expressions", "description": "A JSX to DOM plugin that wraps expressions for fine grained change detection", "version": "0.40.1", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ryansolid/dom-expressions/blob/main/packages/babel-plugin-jsx-dom-expressions"}, "readmeFilename": "README.md", "main": "index.js", "sideEffects": false, "scripts": {"build": "rollup -c --bundleConfigAsCjs", "test": "pnpm run build && jest --no-cache", "test:coverage": "pnpm run build && jest --coverage --no-cache", "prepublishOnly": "pnpm run build", "prepare": "pnpm run build"}, "dependencies": {"@babel/helper-module-imports": "7.18.6", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/types": "^7.20.7", "html-entities": "2.3.3", "parse5": "^7.1.2", "validate-html-nesting": "^1.2.1"}, "peerDependencies": {"@babel/core": "^7.20.12"}, "devDependencies": {"@babel/core": "^7.20.12", "@rollup/plugin-babel": "6.0.3", "@types/babel__core": "^7.20.5"}, "gitHead": "f362e36d1ee2ff4d1ed9f2235ba5b9c174048be5"}