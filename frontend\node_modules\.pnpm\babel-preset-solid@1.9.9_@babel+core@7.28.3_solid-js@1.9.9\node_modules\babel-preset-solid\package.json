{"name": "babel-preset-solid", "version": "1.9.9", "description": "Babel preset to transform JSX for Solid.js", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/solidjs/solid/blob/main/packages/babel-preset-solid#readme", "license": "MIT", "repository": "https://github.com/solidjs/solid/blob/main/packages/babel-preset-solid", "main": "index.js", "files": ["index.js"], "dependencies": {"babel-plugin-jsx-dom-expressions": "^0.40.1"}, "peerDependencies": {"@babel/core": "^7.0.0", "solid-js": "^1.9.8"}, "peerDependenciesMeta": {"solid-js": {"optional": true}}, "scripts": {"test": "node test.js"}}