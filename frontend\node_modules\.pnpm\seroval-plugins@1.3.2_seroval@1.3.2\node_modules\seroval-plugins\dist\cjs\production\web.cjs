"use strict";var c=Object.defineProperty;var J=Object.getOwnPropertyDescriptor;var K=Object.getOwnPropertyNames;var Q=Object.prototype.hasOwnProperty;var X=(e,r)=>{for(var a in r)c(e,a,{get:r[a],enumerable:!0})},Z=(e,r,a,t)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of K(r))!Q.call(e,n)&&n!==a&&c(e,n,{get:()=>r[n],enumerable:!(t=J(r,n))||t.enumerable});return e};var $=e=>Z(c({},"__esModule",{value:!0}),e);var ze={};X(ze,{AbortSignalPlugin:()=>v,BlobPlugin:()=>N,CustomEventPlugin:()=>h,DOMExceptionPlugin:()=>E,EventPlugin:()=>D,FilePlugin:()=>m,FormDataPlugin:()=>C,HeadersPlugin:()=>o,ImageDataPlugin:()=>O,ReadableStreamPlugin:()=>i,RequestPlugin:()=>M,ResponsePlugin:()=>k,URLPlugin:()=>Y,URLSearchParamsPlugin:()=>W});module.exports=$(ze);var f=require("seroval");function x(e){e(this.reason)}function ee(e){this.addEventListener("abort",x.bind(this,e),{once:!0})}function re(e){return new Promise(ee.bind(e))}var l=class{constructor(){this.controller=new AbortController}},ae=(0,f.createPlugin)({tag:"seroval-plugins/web/AbortSignalController",test(e){return e instanceof l},parse:{stream(){}},serialize(e){return"new AbortController"},deserialize(e){return new l}}),p=class{constructor(r,a){this.controller=r;this.reason=a}},te=(0,f.createPlugin)({extends:[ae],tag:"seroval-plugins/web/AbortSignalAbort",test(e){return e instanceof p},parse:{stream(e,r){return{controller:r.parse(e.controller),reason:r.parse(e.reason)}}},serialize(e,r){return r.serialize(e.controller)+".abort("+r.serialize(e.reason)+")"},deserialize(e,r){let a=r.deserialize(e.controller),t=r.deserialize(e.reason);return a.controller.abort(t),new p(a,t)}});var ne=(0,f.createPlugin)({tag:"seroval-plugins/web/AbortSignal",extends:[te],test(e){return typeof AbortSignal=="undefined"?!1:e instanceof AbortSignal},parse:{sync(e,r){return e.aborted?{type:1,reason:r.parse(e.reason)}:{type:0}},async async(e,r){if(e.aborted)return{type:1,reason:await r.parse(e.reason)};let a=await re(e);return{type:1,reason:await r.parse(a)}},stream(e,r){if(e.aborted)return{type:1,reason:r.parse(e.reason)};let a=new l;return r.pushPendingState(),e.addEventListener("abort",()=>{let t=r.parseWithError(new p(a,e.reason));t&&r.onParse(t),r.popPendingState()},{once:!0}),{type:2,controller:r.parse(a)}}},serialize(e,r){return e.type===0?"(new AbortController).signal":e.type===1?"AbortSignal.abort("+r.serialize(e.reason)+")":"("+r.serialize(e.controller)+").signal"},deserialize(e,r){return e.type===0?new AbortController().signal:e.type===1?AbortSignal.abort(r.deserialize(e.reason)):r.deserialize(e.controller).controller.signal}}),v=ne;var P=require("seroval"),oe=(0,P.createPlugin)({tag:"seroval-plugins/web/Blob",test(e){return typeof Blob=="undefined"?!1:e instanceof Blob},parse:{async async(e,r){return{type:await r.parse(e.type),buffer:await r.parse(await e.arrayBuffer())}}},serialize(e,r){return"new Blob(["+r.serialize(e.buffer)+"],{type:"+r.serialize(e.type)+"})"},deserialize(e,r){return new Blob([r.deserialize(e.buffer)],{type:r.deserialize(e.type)})}}),N=oe;var R=require("seroval");function g(e){return{detail:e.detail,bubbles:e.bubbles,cancelable:e.cancelable,composed:e.composed}}var se=(0,R.createPlugin)({tag:"seroval-plugins/web/CustomEvent",test(e){return typeof CustomEvent=="undefined"?!1:e instanceof CustomEvent},parse:{sync(e,r){return{type:r.parse(e.type),options:r.parse(g(e))}},async async(e,r){return{type:await r.parse(e.type),options:await r.parse(g(e))}},stream(e,r){return{type:r.parse(e.type),options:r.parse(g(e))}}},serialize(e,r){return"new CustomEvent("+r.serialize(e.type)+","+r.serialize(e.options)+")"},deserialize(e,r){return new CustomEvent(r.deserialize(e.type),r.deserialize(e.options))}}),h=se;var A=require("seroval"),ie=(0,A.createPlugin)({tag:"seroval-plugins/web/DOMException",test(e){return typeof DOMException=="undefined"?!1:e instanceof DOMException},parse:{sync(e,r){return{name:r.parse(e.name),message:r.parse(e.message)}},async async(e,r){return{name:await r.parse(e.name),message:await r.parse(e.message)}},stream(e,r){return{name:r.parse(e.name),message:r.parse(e.message)}}},serialize(e,r){return"new DOMException("+r.serialize(e.message)+","+r.serialize(e.name)+")"},deserialize(e,r){return new DOMException(r.deserialize(e.message),r.deserialize(e.name))}}),E=ie;var F=require("seroval");function y(e){return{bubbles:e.bubbles,cancelable:e.cancelable,composed:e.composed}}var le=(0,F.createPlugin)({tag:"seroval-plugins/web/Event",test(e){return typeof Event=="undefined"?!1:e instanceof Event},parse:{sync(e,r){return{type:r.parse(e.type),options:r.parse(y(e))}},async async(e,r){return{type:await r.parse(e.type),options:await r.parse(y(e))}},stream(e,r){return{type:r.parse(e.type),options:r.parse(y(e))}}},serialize(e,r){return"new Event("+r.serialize(e.type)+","+r.serialize(e.options)+")"},deserialize(e,r){return new Event(r.deserialize(e.type),r.deserialize(e.options))}}),D=le;var I=require("seroval"),pe=(0,I.createPlugin)({tag:"seroval-plugins/web/File",test(e){return typeof File=="undefined"?!1:e instanceof File},parse:{async async(e,r){return{name:await r.parse(e.name),options:await r.parse({type:e.type,lastModified:e.lastModified}),buffer:await r.parse(await e.arrayBuffer())}}},serialize(e,r){return"new File(["+r.serialize(e.buffer)+"],"+r.serialize(e.name)+","+r.serialize(e.options)+")"},deserialize(e,r){return new File([r.deserialize(e.buffer)],r.deserialize(e.name),r.deserialize(e.options))}}),m=pe;var S=require("seroval");function b(e){let r=[];return e.forEach((a,t)=>{r.push([t,a])}),r}var u={},ue=(0,S.createPlugin)({tag:"seroval-plugins/web/FormDataFactory",test(e){return e===u},parse:{sync(){},async async(){return await Promise.resolve(void 0)},stream(){}},serialize(e,r){return r.createEffectfulFunction(["e","f","i","s","t"],"f=new FormData;for(i=0,s=e.length;i<s;i++)f.append((t=e[i])[0],t[1]);return f")},deserialize(){return u}}),de=(0,S.createPlugin)({tag:"seroval-plugins/web/FormData",extends:[m,ue],test(e){return typeof FormData=="undefined"?!1:e instanceof FormData},parse:{sync(e,r){return{factory:r.parse(u),entries:r.parse(b(e))}},async async(e,r){return{factory:await r.parse(u),entries:await r.parse(b(e))}},stream(e,r){return{factory:r.parse(u),entries:r.parse(b(e))}}},serialize(e,r){return"("+r.serialize(e.factory)+")("+r.serialize(e.entries)+")"},deserialize(e,r){let a=new FormData,t=r.deserialize(e.entries);for(let n=0,G=t.length;n<G;n++){let z=t[n];a.append(z[0],z[1])}return a}}),C=de;var B=require("seroval");function w(e){let r=[];return e.forEach((a,t)=>{r.push([t,a])}),r}var fe=(0,B.createPlugin)({tag:"seroval-plugins/web/Headers",test(e){return typeof Headers=="undefined"?!1:e instanceof Headers},parse:{sync(e,r){return r.parse(w(e))},async async(e,r){return await r.parse(w(e))},stream(e,r){return r.parse(w(e))}},serialize(e,r){return"new Headers("+r.serialize(e)+")"},deserialize(e,r){return new Headers(r.deserialize(e))}}),o=fe;var L=require("seroval"),me=(0,L.createPlugin)({tag:"seroval-plugins/web/ImageData",test(e){return typeof ImageData=="undefined"?!1:e instanceof ImageData},parse:{sync(e,r){return{data:r.parse(e.data),width:r.parse(e.width),height:r.parse(e.height),options:r.parse({colorSpace:e.colorSpace})}},async async(e,r){return{data:await r.parse(e.data),width:await r.parse(e.width),height:await r.parse(e.height),options:await r.parse({colorSpace:e.colorSpace})}},stream(e,r){return{data:r.parse(e.data),width:r.parse(e.width),height:r.parse(e.height),options:r.parse({colorSpace:e.colorSpace})}}},serialize(e,r){return"new ImageData("+r.serialize(e.data)+","+r.serialize(e.width)+","+r.serialize(e.height)+","+r.serialize(e.options)+")"},deserialize(e,r){return new ImageData(r.deserialize(e.data),r.deserialize(e.width),r.deserialize(e.height),r.deserialize(e.options))}}),O=me;var s=require("seroval"),d={},ce=(0,s.createPlugin)({tag:"seroval-plugins/web/ReadableStreamFactory",test(e){return e===d},parse:{sync(){},async async(){return await Promise.resolve(void 0)},stream(){}},serialize(e,r){return r.createFunction(["d"],"new ReadableStream({start:"+r.createEffectfulFunction(["c"],"d.on({next:"+r.createEffectfulFunction(["v"],"c.enqueue(v)")+",throw:"+r.createEffectfulFunction(["v"],"c.error(v)")+",return:"+r.createEffectfulFunction([],"c.close()")+"})")+"})")},deserialize(){return d}});function U(e){let r=(0,s.createStream)(),a=e.getReader();async function t(){try{let n=await a.read();n.done?r.return(n.value):(r.next(n.value),await t())}catch(n){r.throw(n)}}return t().catch(()=>{}),r}var ge=(0,s.createPlugin)({tag:"seroval/plugins/web/ReadableStream",extends:[ce],test(e){return typeof ReadableStream=="undefined"?!1:e instanceof ReadableStream},parse:{sync(e,r){return{factory:r.parse(d),stream:r.parse((0,s.createStream)())}},async async(e,r){return{factory:await r.parse(d),stream:await r.parse(U(e))}},stream(e,r){return{factory:r.parse(d),stream:r.parse(U(e))}}},serialize(e,r){return"("+r.serialize(e.factory)+")("+r.serialize(e.stream)+")"},deserialize(e,r){let a=r.deserialize(e.stream);return new ReadableStream({start(t){a.on({next(n){t.enqueue(n)},throw(n){t.error(n)},return(){t.close()}})}})}}),i=ge;var H=require("seroval");function q(e,r){return{body:r,cache:e.cache,credentials:e.credentials,headers:e.headers,integrity:e.integrity,keepalive:e.keepalive,method:e.method,mode:e.mode,redirect:e.redirect,referrer:e.referrer,referrerPolicy:e.referrerPolicy}}var ye=(0,H.createPlugin)({tag:"seroval-plugins/web/Request",extends:[i,o],test(e){return typeof Request=="undefined"?!1:e instanceof Request},parse:{async async(e,r){return{url:await r.parse(e.url),options:await r.parse(q(e,e.body?await e.clone().arrayBuffer():null))}},stream(e,r){return{url:r.parse(e.url),options:r.parse(q(e,e.clone().body))}}},serialize(e,r){return"new Request("+r.serialize(e.url)+","+r.serialize(e.options)+")"},deserialize(e,r){return new Request(r.deserialize(e.url),r.deserialize(e.options))}}),M=ye;var _=require("seroval");function T(e){return{headers:e.headers,status:e.status,statusText:e.statusText}}var be=(0,_.createPlugin)({tag:"seroval-plugins/web/Response",extends:[i,o],test(e){return typeof Response=="undefined"?!1:e instanceof Response},parse:{async async(e,r){return{body:await r.parse(e.body?await e.clone().arrayBuffer():null),options:await r.parse(T(e))}},stream(e,r){return{body:r.parse(e.clone().body),options:r.parse(T(e))}}},serialize(e,r){return"new Response("+r.serialize(e.body)+","+r.serialize(e.options)+")"},deserialize(e,r){return new Response(r.deserialize(e.body),r.deserialize(e.options))}}),k=be;var j=require("seroval"),Se=(0,j.createPlugin)({tag:"seroval-plugins/web/URL",test(e){return typeof URL=="undefined"?!1:e instanceof URL},parse:{sync(e,r){return r.parse(e.href)},async async(e,r){return await r.parse(e.href)},stream(e,r){return r.parse(e.href)}},serialize(e,r){return"new URL("+r.serialize(e)+")"},deserialize(e,r){return new URL(r.deserialize(e))}}),Y=Se;var V=require("seroval"),we=(0,V.createPlugin)({tag:"seroval-plugins/web/URLSearchParams",test(e){return typeof URLSearchParams=="undefined"?!1:e instanceof URLSearchParams},parse:{sync(e,r){return r.parse(e.toString())},async async(e,r){return await r.parse(e.toString())},stream(e,r){return r.parse(e.toString())}},serialize(e,r){return"new URLSearchParams("+r.serialize(e)+")"},deserialize(e,r){return new URLSearchParams(r.deserialize(e))}}),W=we;
