import{createPlugin as d}from"seroval";function h(e){e(this.reason)}function A(e){this.addEventListener("abort",h.bind(this,e),{once:!0})}function E(e){return new Promise(A.bind(e))}var o=class{constructor(){this.controller=new AbortController}},F=d({tag:"seroval-plugins/web/AbortSignalController",test(e){return e instanceof o},parse:{stream(){}},serialize(e){return"new AbortController"},deserialize(e){return new o}}),s=class{constructor(r,a){this.controller=r;this.reason=a}},D=d({extends:[F],tag:"seroval-plugins/web/AbortSignalAbort",test(e){return e instanceof s},parse:{stream(e,r){return{controller:r.parse(e.controller),reason:r.parse(e.reason)}}},serialize(e,r){return r.serialize(e.controller)+".abort("+r.serialize(e.reason)+")"},deserialize(e,r){let a=r.deserialize(e.controller),t=r.deserialize(e.reason);return a.controller.abort(t),new s(a,t)}});var I=d({tag:"seroval-plugins/web/AbortSignal",extends:[D],test(e){return typeof AbortSignal=="undefined"?!1:e instanceof AbortSignal},parse:{sync(e,r){return e.aborted?{type:1,reason:r.parse(e.reason)}:{type:0}},async async(e,r){if(e.aborted)return{type:1,reason:await r.parse(e.reason)};let a=await E(e);return{type:1,reason:await r.parse(a)}},stream(e,r){if(e.aborted)return{type:1,reason:r.parse(e.reason)};let a=new o;return r.pushPendingState(),e.addEventListener("abort",()=>{let t=r.parseWithError(new s(a,e.reason));t&&r.onParse(t),r.popPendingState()},{once:!0}),{type:2,controller:r.parse(a)}}},serialize(e,r){return e.type===0?"(new AbortController).signal":e.type===1?"AbortSignal.abort("+r.serialize(e.reason)+")":"("+r.serialize(e.controller)+").signal"},deserialize(e,r){return e.type===0?new AbortController().signal:e.type===1?AbortSignal.abort(r.deserialize(e.reason)):r.deserialize(e.controller).controller.signal}}),C=I;import{createPlugin as B}from"seroval";var L=B({tag:"seroval-plugins/web/Blob",test(e){return typeof Blob=="undefined"?!1:e instanceof Blob},parse:{async async(e,r){return{type:await r.parse(e.type),buffer:await r.parse(await e.arrayBuffer())}}},serialize(e,r){return"new Blob(["+r.serialize(e.buffer)+"],{type:"+r.serialize(e.type)+"})"},deserialize(e,r){return new Blob([r.deserialize(e.buffer)],{type:r.deserialize(e.type)})}}),O=L;import{createPlugin as U}from"seroval";function f(e){return{detail:e.detail,bubbles:e.bubbles,cancelable:e.cancelable,composed:e.composed}}var q=U({tag:"seroval-plugins/web/CustomEvent",test(e){return typeof CustomEvent=="undefined"?!1:e instanceof CustomEvent},parse:{sync(e,r){return{type:r.parse(e.type),options:r.parse(f(e))}},async async(e,r){return{type:await r.parse(e.type),options:await r.parse(f(e))}},stream(e,r){return{type:r.parse(e.type),options:r.parse(f(e))}}},serialize(e,r){return"new CustomEvent("+r.serialize(e.type)+","+r.serialize(e.options)+")"},deserialize(e,r){return new CustomEvent(r.deserialize(e.type),r.deserialize(e.options))}}),H=q;import{createPlugin as M}from"seroval";var T=M({tag:"seroval-plugins/web/DOMException",test(e){return typeof DOMException=="undefined"?!1:e instanceof DOMException},parse:{sync(e,r){return{name:r.parse(e.name),message:r.parse(e.message)}},async async(e,r){return{name:await r.parse(e.name),message:await r.parse(e.message)}},stream(e,r){return{name:r.parse(e.name),message:r.parse(e.message)}}},serialize(e,r){return"new DOMException("+r.serialize(e.message)+","+r.serialize(e.name)+")"},deserialize(e,r){return new DOMException(r.deserialize(e.message),r.deserialize(e.name))}}),_=T;import{createPlugin as k}from"seroval";function m(e){return{bubbles:e.bubbles,cancelable:e.cancelable,composed:e.composed}}var j=k({tag:"seroval-plugins/web/Event",test(e){return typeof Event=="undefined"?!1:e instanceof Event},parse:{sync(e,r){return{type:r.parse(e.type),options:r.parse(m(e))}},async async(e,r){return{type:await r.parse(e.type),options:await r.parse(m(e))}},stream(e,r){return{type:r.parse(e.type),options:r.parse(m(e))}}},serialize(e,r){return"new Event("+r.serialize(e.type)+","+r.serialize(e.options)+")"},deserialize(e,r){return new Event(r.deserialize(e.type),r.deserialize(e.options))}}),Y=j;import{createPlugin as V}from"seroval";var W=V({tag:"seroval-plugins/web/File",test(e){return typeof File=="undefined"?!1:e instanceof File},parse:{async async(e,r){return{name:await r.parse(e.name),options:await r.parse({type:e.type,lastModified:e.lastModified}),buffer:await r.parse(await e.arrayBuffer())}}},serialize(e,r){return"new File(["+r.serialize(e.buffer)+"],"+r.serialize(e.name)+","+r.serialize(e.options)+")"},deserialize(e,r){return new File([r.deserialize(e.buffer)],r.deserialize(e.name),r.deserialize(e.options))}}),c=W;import{createPlugin as S}from"seroval";function g(e){let r=[];return e.forEach((a,t)=>{r.push([t,a])}),r}var i={},G=S({tag:"seroval-plugins/web/FormDataFactory",test(e){return e===i},parse:{sync(){},async async(){return await Promise.resolve(void 0)},stream(){}},serialize(e,r){return r.createEffectfulFunction(["e","f","i","s","t"],"f=new FormData;for(i=0,s=e.length;i<s;i++)f.append((t=e[i])[0],t[1]);return f")},deserialize(){return i}}),J=S({tag:"seroval-plugins/web/FormData",extends:[c,G],test(e){return typeof FormData=="undefined"?!1:e instanceof FormData},parse:{sync(e,r){return{factory:r.parse(i),entries:r.parse(g(e))}},async async(e,r){return{factory:await r.parse(i),entries:await r.parse(g(e))}},stream(e,r){return{factory:r.parse(i),entries:r.parse(g(e))}}},serialize(e,r){return"("+r.serialize(e.factory)+")("+r.serialize(e.entries)+")"},deserialize(e,r){let a=new FormData,t=r.deserialize(e.entries);for(let n=0,R=t.length;n<R;n++){let b=t[n];a.append(b[0],b[1])}return a}}),K=J;import{createPlugin as Q}from"seroval";function y(e){let r=[];return e.forEach((a,t)=>{r.push([t,a])}),r}var X=Q({tag:"seroval-plugins/web/Headers",test(e){return typeof Headers=="undefined"?!1:e instanceof Headers},parse:{sync(e,r){return r.parse(y(e))},async async(e,r){return await r.parse(y(e))},stream(e,r){return r.parse(y(e))}},serialize(e,r){return"new Headers("+r.serialize(e)+")"},deserialize(e,r){return new Headers(r.deserialize(e))}}),l=X;import{createPlugin as Z}from"seroval";var $=Z({tag:"seroval-plugins/web/ImageData",test(e){return typeof ImageData=="undefined"?!1:e instanceof ImageData},parse:{sync(e,r){return{data:r.parse(e.data),width:r.parse(e.width),height:r.parse(e.height),options:r.parse({colorSpace:e.colorSpace})}},async async(e,r){return{data:await r.parse(e.data),width:await r.parse(e.width),height:await r.parse(e.height),options:await r.parse({colorSpace:e.colorSpace})}},stream(e,r){return{data:r.parse(e.data),width:r.parse(e.width),height:r.parse(e.height),options:r.parse({colorSpace:e.colorSpace})}}},serialize(e,r){return"new ImageData("+r.serialize(e.data)+","+r.serialize(e.width)+","+r.serialize(e.height)+","+r.serialize(e.options)+")"},deserialize(e,r){return new ImageData(r.deserialize(e.data),r.deserialize(e.width),r.deserialize(e.height),r.deserialize(e.options))}}),x=$;import{createPlugin as z,createStream as v}from"seroval";var p={},ee=z({tag:"seroval-plugins/web/ReadableStreamFactory",test(e){return e===p},parse:{sync(){},async async(){return await Promise.resolve(void 0)},stream(){}},serialize(e,r){return r.createFunction(["d"],"new ReadableStream({start:"+r.createEffectfulFunction(["c"],"d.on({next:"+r.createEffectfulFunction(["v"],"c.enqueue(v)")+",throw:"+r.createEffectfulFunction(["v"],"c.error(v)")+",return:"+r.createEffectfulFunction([],"c.close()")+"})")+"})")},deserialize(){return p}});function w(e){let r=v(),a=e.getReader();async function t(){try{let n=await a.read();n.done?r.return(n.value):(r.next(n.value),await t())}catch(n){r.throw(n)}}return t().catch(()=>{}),r}var re=z({tag:"seroval/plugins/web/ReadableStream",extends:[ee],test(e){return typeof ReadableStream=="undefined"?!1:e instanceof ReadableStream},parse:{sync(e,r){return{factory:r.parse(p),stream:r.parse(v())}},async async(e,r){return{factory:await r.parse(p),stream:await r.parse(w(e))}},stream(e,r){return{factory:r.parse(p),stream:r.parse(w(e))}}},serialize(e,r){return"("+r.serialize(e.factory)+")("+r.serialize(e.stream)+")"},deserialize(e,r){let a=r.deserialize(e.stream);return new ReadableStream({start(t){a.on({next(n){t.enqueue(n)},throw(n){t.error(n)},return(){t.close()}})}})}}),u=re;import{createPlugin as ae}from"seroval";function P(e,r){return{body:r,cache:e.cache,credentials:e.credentials,headers:e.headers,integrity:e.integrity,keepalive:e.keepalive,method:e.method,mode:e.mode,redirect:e.redirect,referrer:e.referrer,referrerPolicy:e.referrerPolicy}}var te=ae({tag:"seroval-plugins/web/Request",extends:[u,l],test(e){return typeof Request=="undefined"?!1:e instanceof Request},parse:{async async(e,r){return{url:await r.parse(e.url),options:await r.parse(P(e,e.body?await e.clone().arrayBuffer():null))}},stream(e,r){return{url:r.parse(e.url),options:r.parse(P(e,e.clone().body))}}},serialize(e,r){return"new Request("+r.serialize(e.url)+","+r.serialize(e.options)+")"},deserialize(e,r){return new Request(r.deserialize(e.url),r.deserialize(e.options))}}),ne=te;import{createPlugin as oe}from"seroval";function N(e){return{headers:e.headers,status:e.status,statusText:e.statusText}}var se=oe({tag:"seroval-plugins/web/Response",extends:[u,l],test(e){return typeof Response=="undefined"?!1:e instanceof Response},parse:{async async(e,r){return{body:await r.parse(e.body?await e.clone().arrayBuffer():null),options:await r.parse(N(e))}},stream(e,r){return{body:r.parse(e.clone().body),options:r.parse(N(e))}}},serialize(e,r){return"new Response("+r.serialize(e.body)+","+r.serialize(e.options)+")"},deserialize(e,r){return new Response(r.deserialize(e.body),r.deserialize(e.options))}}),ie=se;import{createPlugin as le}from"seroval";var pe=le({tag:"seroval-plugins/web/URL",test(e){return typeof URL=="undefined"?!1:e instanceof URL},parse:{sync(e,r){return r.parse(e.href)},async async(e,r){return await r.parse(e.href)},stream(e,r){return r.parse(e.href)}},serialize(e,r){return"new URL("+r.serialize(e)+")"},deserialize(e,r){return new URL(r.deserialize(e))}}),ue=pe;import{createPlugin as de}from"seroval";var fe=de({tag:"seroval-plugins/web/URLSearchParams",test(e){return typeof URLSearchParams=="undefined"?!1:e instanceof URLSearchParams},parse:{sync(e,r){return r.parse(e.toString())},async async(e,r){return await r.parse(e.toString())},stream(e,r){return r.parse(e.toString())}},serialize(e,r){return"new URLSearchParams("+r.serialize(e)+")"},deserialize(e,r){return new URLSearchParams(r.deserialize(e))}}),me=fe;export{C as AbortSignalPlugin,O as BlobPlugin,H as CustomEventPlugin,_ as DOMExceptionPlugin,Y as EventPlugin,c as FilePlugin,K as FormDataPlugin,l as HeadersPlugin,x as ImageDataPlugin,u as ReadableStreamPlugin,ne as RequestPlugin,ie as ResponsePlugin,ue as URLPlugin,me as URLSearchParamsPlugin};
