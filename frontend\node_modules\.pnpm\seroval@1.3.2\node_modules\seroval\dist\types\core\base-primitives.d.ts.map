{"version": 3, "file": "base-primitives.d.ts", "sourceRoot": "", "sources": ["../../../src/core/base-primitives.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAWpD,OAAO,KAAK,EACV,yBAAyB,EACzB,sBAAsB,EACtB,gBAAgB,EAChB,uCAAuC,EACvC,iBAAiB,EACjB,2BAA2B,EAC3B,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,EACf,gBAAgB,EAChB,uBAAuB,EACvB,kCAAkC,EAClC,WAAW,EACX,iBAAiB,EACjB,iBAAiB,EACjB,uBAAuB,EACvB,iBAAiB,EACjB,oBAAoB,EACpB,iBAAiB,EACjB,cAAc,EACd,4BAA4B,EAC5B,qBAAqB,EACrB,uBAAuB,EACvB,sBAAsB,EACtB,iBAAiB,EACjB,qBAAqB,EACrB,mBAAmB,EACpB,MAAM,SAAS,CAAC;AAGjB,OAAO,KAAK,EACV,qBAAqB,EACrB,eAAe,EAChB,MAAM,qBAAqB,CAAC;AAE7B,wBAAgB,gBAAgB,CAC9B,KAAK,EAAE,MAAM,GACZ,mBAAmB,GAAG,iBAAiB,CA2BzC;AAED,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,MAAM,GAAG,iBAAiB,CAejE;AAED,wBAAgB,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,iBAAiB,CAenE;AAED,wBAAgB,sBAAsB,CAAC,EAAE,EAAE,MAAM,GAAG,uBAAuB,CAe1E;AAED,wBAAgB,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,GAAG,eAAe,CAgBzE;AAED,wBAAgB,gBAAgB,CAC9B,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,MAAM,GACd,iBAAiB,CAenB;AAED,wBAAgB,qBAAqB,CACnC,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,WAAW,GACnB,sBAAsB,CAqBxB;AAED,wBAAgB,kBAAkB,CAChC,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,gBAAgB,GACxB,mBAAmB,CAerB;AAED,wBAAgB,mBAAmB,CAAC,CAAC,EACnC,EAAE,EAAE,MAAM,EACV,GAAG,EAAE,CAAC,GACL,oBAAoB,CAetB;AAED,wBAAgB,gBAAgB,CAC9B,EAAE,EAAE,MAAM,EACV,GAAG,EAAE,MAAM,EACX,KAAK,EAAE,OAAO,GACb,iBAAiB,CAenB;AAED,wBAAgB,eAAe,CAC7B,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,OAAO,EAAE,EAClB,WAAW,EAAE,WAAW,EAAE,GACzB,gBAAgB,CAelB;AAED,wBAAgB,eAAe,CAC7B,EAAE,EAAE,MAAM,EACV,KAAK,EAAE,WAAW,GACjB,gBAAgB,CAelB;AAED,wBAAgB,oBAAoB,CAClC,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,eAAe,EACxB,MAAM,EAAE,WAAW,GAClB,qBAAqB,CAevB;AAED,wBAAgB,0BAA0B,CACxC,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,qBAAqB,EAC9B,MAAM,EAAE,WAAW,GAClB,2BAA2B,CAe7B;AAED,wBAAgB,kBAAkB,CAChC,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,QAAQ,EACjB,MAAM,EAAE,WAAW,GAClB,mBAAmB,CAerB;AAED,wBAAgB,eAAe,CAC7B,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,KAAK,EACd,OAAO,EAAE,uBAAuB,GAAG,SAAS,GAC3C,gBAAgB,CAelB;AAED,wBAAgB,wBAAwB,CACtC,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,cAAc,EACvB,OAAO,EAAE,uBAAuB,GAAG,SAAS,GAC3C,yBAAyB,CAe3B;AAED,wBAAgB,aAAa,CAC3B,EAAE,EAAE,MAAM,EACV,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,WAAW,EAAE,GACnB,cAAc,CAehB;AAED,wBAAgB,iCAAiC,CAC/C,OAAO,EAAE,iBAAiB,EAC1B,KAAK,EAAE,WAAW,GACjB,kCAAkC,CAepC;AAED,wBAAgB,sCAAsC,CACpD,OAAO,EAAE,iBAAiB,EAC1B,KAAK,EAAE,WAAW,GACjB,uCAAuC,CAezC;AAED,wBAAgB,2BAA2B,CACzC,EAAE,EAAE,MAAM,EACV,OAAO,EAAE,iBAAiB,EAC1B,QAAQ,EAAE,WAAW,EAAE,GACtB,4BAA4B,CAe9B;AAED,wBAAgB,oBAAoB,CAClC,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,WAAW,GAClB,qBAAqB,CAevB;AAED,wBAAgB,qBAAqB,CACnC,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,WAAW,GAClB,sBAAsB,CAexB;AAED,wBAAgB,sBAAsB,CACpC,EAAE,EAAE,MAAM,EACV,MAAM,EAAE,WAAW,GAClB,uBAAuB,CAezB"}