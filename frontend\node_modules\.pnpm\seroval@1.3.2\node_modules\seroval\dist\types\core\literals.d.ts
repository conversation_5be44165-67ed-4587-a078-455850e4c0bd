import type { SerovalConstantNode } from './types';
export declare const TRUE_NODE: SerovalConstantNode;
export declare const FALSE_NODE: SerovalConstantNode;
export declare const UNDEFINED_NODE: SerovalConstantNode;
export declare const NULL_NODE: SerovalConstantNode;
export declare const NEG_ZERO_NODE: SerovalConstantNode;
export declare const INFINITY_NODE: SerovalConstantNode;
export declare const NEG_INFINITY_NODE: SerovalConstantNode;
export declare const NAN_NODE: SerovalConstantNode;
//# sourceMappingURL=literals.d.ts.map