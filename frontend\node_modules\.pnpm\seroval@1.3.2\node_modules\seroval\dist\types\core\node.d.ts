import type { SerovalNodeType } from './constants';
import type { SerovalNode } from './types';
type ExtractedNodeType<T extends SerovalNodeType> = Extract<SerovalNode, {
    t: T;
}>;
export declare function createSerovalNode<T extends SerovalNodeType, N extends ExtractedNodeType<T>>(t: T, i: N['i'], s: N['s'], l: N['l'], c: N['c'], m: N['m'], p: N['p'], e: N['e'], a: N['a'], f: N['f'], b: N['b'], o: N['o']): N;
export {};
//# sourceMappingURL=node.d.ts.map