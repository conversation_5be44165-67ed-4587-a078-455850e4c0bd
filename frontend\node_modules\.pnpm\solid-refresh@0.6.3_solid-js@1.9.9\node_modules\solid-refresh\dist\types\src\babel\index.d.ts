import type * as babel from '@babel/core';
import * as t from '@babel/types';
import type { RuntimeType } from '../shared/types';
interface Options {
    bundler?: RuntimeType;
    fixRender?: boolean;
    imports?: ImportIdentity[];
}
type ImportHook = Map<string, t.Identifier>;
interface ImportIdentifiers {
    identifiers: Map<t.Identifier, ImportIdentity>;
    namespaces: Map<t.Identifier, ImportIdentity[]>;
}
interface State extends babel.PluginPass {
    hooks: ImportHook;
    opts: Options;
    processed: boolean;
    granular: boolean;
    registrations: ImportIdentifiers;
    fixRender?: boolean;
    imports: ImportIdentity[];
}
interface ImportIdentity {
    name: string;
    source: string;
    kind: 'named' | 'default';
    type: 'createContext' | 'render';
}
export default function solidRefreshPlugin(): babel.PluginObj<State>;
export {};
//# sourceMappingURL=index.d.ts.map