{"name": "solid-refresh", "type": "module", "description": "Universal HMR for SolidJS", "author": "<PERSON>", "license": "MIT", "version": "0.6.3", "homepage": "https://github.com/solidjs/solid-refresh#readme", "repository": {"type": "git", "url": "https://github.com/solidjs/solid-refresh"}, "main": "dist/solid-refresh.cjs", "module": "dist/solid-refresh.mjs", "exports": {".": {"import": "./dist/solid-refresh.mjs", "require": "./dist/solid-refresh.cjs", "default": "./dist/solid-refresh.cjs", "types": "./dist/src/runtime/index.d.ts"}, "./babel": {"import": "./dist/babel.mjs", "require": "./dist/babel.cjs", "default": "./dist/babel.cjs", "types": "./dist/src/babel/index.d.ts"}, "./dist/*": "./dist/*"}, "typesVersions": {"*": {"babel": ["./dist/src/babel/index.d.ts"]}}, "files": ["dist"], "publishConfig": {"access": "public"}, "contributors": ["<PERSON>"], "sideEffects": false, "devDependencies": {"@babel/core": "^7.23.6", "@biomejs/biome": "^1.5.1", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.6.8", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__traverse": "^7.20.4", "@types/node": "^20.10.5", "babel-preset-solid": "^1.8.9", "rollup": "^4.9.1", "solid-js": "^1.8.7", "tslib": "^2.6.2", "typescript": "^5.3.3", "vitest": "^1.1.0"}, "peerDependencies": {"solid-js": "^1.3"}, "dependencies": {"@babel/generator": "^7.23.6", "@babel/helper-module-imports": "^7.22.15", "@babel/types": "^7.23.6"}, "scripts": {"build": "rollup -c", "test": "vitest", "test:CI": "vitest"}}