{"name": "validate-html-nesting", "version": "1.2.3", "description": "Validate parent-child nesting for HTML elements", "main": "src/index.js", "types": "./src/types.d.ts", "scripts": {"test": "jest"}, "keywords": ["html validator", "dom validator"], "author": "Manan <PERSON>", "repository": {"type": "git", "url": "https://github.com/MananTank/validate-html-nesting.git"}, "license": "ISC", "devDependencies": {"@types/jest": "^27.5.1", "jest": "^28.1.0"}}