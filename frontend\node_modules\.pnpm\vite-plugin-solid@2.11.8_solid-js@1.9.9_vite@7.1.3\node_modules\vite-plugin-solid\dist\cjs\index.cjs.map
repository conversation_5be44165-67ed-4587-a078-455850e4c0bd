{"version": 3, "file": "index.cjs", "sources": ["../../src/index.ts"], "sourcesContent": ["import * as babel from '@babel/core';\nimport solid from 'babel-preset-solid';\nimport { readFileSync } from 'fs';\nimport { mergeAndConcat } from 'merge-anything';\nimport { createRequire } from 'module';\nimport solidRefresh from 'solid-refresh/babel';\nimport type { <PERSON><PERSON>, AliasOptions, FilterPattern, Plugin } from 'vite';\nimport { createFilter, version } from 'vite';\nimport { crawlFrameworkPkgs } from 'vitefu';\n\nconst require = createRequire(import.meta.url);\n\nconst runtimePublicPath = '/@solid-refresh';\nconst runtimeFilePath = require.resolve('solid-refresh/dist/solid-refresh.mjs');\nconst runtimeCode = readFileSync(runtimeFilePath, 'utf-8');\n\nconst isVite6 = +version.split('.')[0] >= 6;\n\n/** Possible options for the extensions property */\nexport interface ExtensionOptions {\n  typescript?: boolean;\n}\n\n/** Configuration options for vite-plugin-solid. */\nexport interface Options {\n  /**\n   * A [picomatch](https://github.com/micromatch/picomatch) pattern, or array of patterns, which specifies the files\n   * the plugin should operate on.\n   */\n  include?: FilterPattern;\n  /**\n   * A [picomatch](https://github.com/micromatch/picomatch) pattern, or array of patterns, which specifies the files\n   * to be ignored by the plugin.\n   */\n  exclude?: FilterPattern;\n  /**\n   * This will inject solid-js/dev in place of solid-js in dev mode. Has no\n   * effect in prod. If set to `false`, it won't inject it in dev. This is\n   * useful for extra logs and debugging.\n   *\n   * @default true\n   */\n  dev?: boolean;\n  /**\n   * This will force SSR code in the produced files.\n   *\n   * @default false\n   */\n  ssr?: boolean;\n\n  /**\n   * This will inject HMR runtime in dev mode. Has no effect in prod. If\n   * set to `false`, it won't inject the runtime in dev.\n   *\n   * @default true\n   */\n  hot?: boolean;\n  /**\n   * This registers additional extensions that should be processed by\n   * vite-plugin-solid.\n   *\n   * @default undefined\n   */\n  extensions?: (string | [string, ExtensionOptions])[];\n  /**\n   * Pass any additional babel transform options. They will be merged with\n   * the transformations required by Solid.\n   *\n   * @default {}\n   */\n  babel?:\n    | babel.TransformOptions\n    | ((source: string, id: string, ssr: boolean) => babel.TransformOptions)\n    | ((source: string, id: string, ssr: boolean) => Promise<babel.TransformOptions>);\n  /**\n   * Pass any additional [babel-plugin-jsx-dom-expressions](https://github.com/ryansolid/dom-expressions/tree/main/packages/babel-plugin-jsx-dom-expressions#plugin-options).\n   * They will be merged with the defaults sets by [babel-preset-solid](https://github.com/solidjs/solid/blob/main/packages/babel-preset-solid/index.js#L8-L25).\n   *\n   * @default {}\n   */\n  solid?: {\n    /**\n     * Remove unnecessary closing tags from template strings. More info here:\n     * https://github.com/solidjs/solid/blob/main/CHANGELOG.md#smaller-templates\n     *\n     * @default false\n     */\n    omitNestedClosingTags?: boolean;\n\n    /**\n     * Remove the last closing tag from template strings. Enabled by default even when `omitNestedClosingTags` is disabled.\n     * Can be disabled for compatibility for some browser-like environments.\n     *\n     * @default true\n     */\n    omitLastClosingTag?: boolean;\n\n    /**\n     * Remove unnecessary quotes from template strings.\n     * Can be disabled for compatibility for some browser-like environments.\n     *\n     * @default true\n     */\n    omitQuotes?: boolean;\n\n    /**\n     * The name of the runtime module to import the methods from.\n     *\n     * @default \"solid-js/web\"\n     */\n    moduleName?: string;\n\n    /**\n     * The output mode of the compiler.\n     * Can be:\n     * - \"dom\" is standard output\n     * - \"ssr\" is for server side rendering of strings.\n     * - \"universal\" is for using custom renderers from solid-js/universal\n     *\n     * @default \"dom\"\n     */\n    generate?: 'ssr' | 'dom' | 'universal';\n\n    /**\n     * Indicate whether the output should contain hydratable markers.\n     *\n     * @default false\n     */\n    hydratable?: boolean;\n\n    /**\n     * Boolean to indicate whether to enable automatic event delegation on camelCase.\n     *\n     * @default true\n     */\n    delegateEvents?: boolean;\n\n    /**\n     * Boolean indicates whether smart conditional detection should be used.\n     * This optimizes simple boolean expressions and ternaries in JSX.\n     *\n     * @default true\n     */\n    wrapConditionals?: boolean;\n\n    /**\n     * Boolean indicates whether to set current render context on Custom Elements and slots.\n     * Useful for seemless Context API with Web Components.\n     *\n     * @default true\n     */\n    contextToCustomElements?: boolean;\n\n    /**\n     * Array of Component exports from module, that aren't included by default with the library.\n     * This plugin will automatically import them if it comes across them in the JSX.\n     *\n     * @default [\"For\",\"Show\",\"Switch\",\"Match\",\"Suspense\",\"SuspenseList\",\"Portal\",\"Index\",\"Dynamic\",\"ErrorBoundary\"]\n     */\n    builtIns?: string[];\n  };\n}\n\nfunction getExtension(filename: string): string {\n  const index = filename.lastIndexOf('.');\n  return index < 0 ? '' : filename.substring(index).replace(/\\?.+$/, '');\n}\nfunction containsSolidField(fields: Record<string, any>) {\n  const keys = Object.keys(fields);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    if (key === 'solid') return true;\n    if (typeof fields[key] === 'object' && fields[key] != null && containsSolidField(fields[key]))\n      return true;\n  }\n  return false;\n}\n\nfunction getJestDomExport(setupFiles: string[]) {\n  return setupFiles?.some((path) => /jest-dom/.test(path))\n    ? undefined\n    : ['@testing-library/jest-dom/vitest', '@testing-library/jest-dom/extend-expect'].find(\n        (path) => {\n          try {\n            require.resolve(path);\n            return true;\n          } catch (e) {\n            return false;\n          }\n        },\n      );\n}\n\nexport default function solidPlugin(options: Partial<Options> = {}): Plugin {\n  const filter = createFilter(options.include, options.exclude);\n\n  let needHmr = false;\n  let replaceDev = false;\n  let projectRoot = process.cwd();\n  let isTestMode = false;\n\n  return {\n    name: 'solid',\n    enforce: 'pre',\n\n    async config(userConfig, { command }) {\n      // We inject the dev mode only if the user explicitly wants it or if we are in dev (serve) mode\n      replaceDev = options.dev === true || (options.dev !== false && command === 'serve');\n      projectRoot = userConfig.root;\n      isTestMode = userConfig.mode === 'test';\n\n      if (!userConfig.resolve) userConfig.resolve = {};\n      userConfig.resolve.alias = normalizeAliases(userConfig.resolve && userConfig.resolve.alias);\n\n      const solidPkgsConfig = await crawlFrameworkPkgs({\n        viteUserConfig: userConfig,\n        root: projectRoot || process.cwd(),\n        isBuild: command === 'build',\n        isFrameworkPkgByJson(pkgJson) {\n          return containsSolidField(pkgJson.exports || {});\n        },\n      });\n\n      // fix for bundling dev in production\n      const nestedDeps = replaceDev\n        ? ['solid-js', 'solid-js/web', 'solid-js/store', 'solid-js/html', 'solid-js/h']\n        : [];\n\n      const userTest = (userConfig as any).test ?? {};\n      const test = {} as any;\n      if (userConfig.mode === 'test') {\n        // to simplify the processing of the config, we normalize the setupFiles to an array\n        const userSetupFiles: string[] =\n          typeof userTest.setupFiles === 'string'\n            ? [userTest.setupFiles]\n            : userTest.setupFiles || [];\n\n        if (!userTest.environment && !options.ssr) {\n          test.environment = 'jsdom';\n        }\n\n        if (\n          !userTest.server?.deps?.external?.find((item: string | RegExp) =>\n            /solid-js/.test(item.toString()),\n          )\n        ) {\n          test.server = { deps: { external: [/solid-js/] } };\n        }\n        if (!userTest.browser?.enabled) {\n          // vitest browser mode already has bundled jest-dom assertions\n          // https://main.vitest.dev/guide/browser/assertion-api.html#assertion-api\n          const jestDomImport = getJestDomExport(userSetupFiles);\n          if (jestDomImport) {\n            test.setupFiles = [jestDomImport];\n          }\n        }\n      }\n\n      return {\n        /**\n         * We only need esbuild on .ts or .js files.\n         * .tsx & .jsx files are handled by us\n         */\n        // esbuild: { include: /\\.ts$/ },\n        resolve: {\n          conditions: isVite6\n            ? undefined\n            : [\n                'solid',\n                ...(replaceDev ? ['development'] : []),\n                ...(userConfig.mode === 'test' && !options.ssr ? ['browser'] : []),\n              ],\n          dedupe: nestedDeps,\n          alias: [{ find: /^solid-refresh$/, replacement: runtimePublicPath }],\n        },\n        optimizeDeps: {\n          include: [...nestedDeps, ...solidPkgsConfig.optimizeDeps.include],\n          exclude: solidPkgsConfig.optimizeDeps.exclude,\n        },\n        ssr: solidPkgsConfig.ssr,\n        ...(test.server ? { test } : {}),\n      };\n    },\n\n    // @ts-ignore This hook only works in Vite 6\n    async configEnvironment(name, config, opts) {\n      config.resolve ??= {};\n      // Emulate Vite default fallback for `resolve.conditions` if not set\n      if (config.resolve.conditions == null) {\n        // @ts-ignore These exports only exist in Vite 6\n        const { defaultClientConditions, defaultServerConditions } = await import('vite');\n        if (config.consumer === 'client' || name === 'client' || opts.isSsrTargetWebworker) {\n          config.resolve.conditions = [...defaultClientConditions];\n        } else {\n          config.resolve.conditions = [...defaultServerConditions];\n        }\n      }\n      config.resolve.conditions = [\n        'solid',\n        ...(replaceDev ? ['development'] : []),\n        ...(isTestMode && !opts.isSsrTargetWebworker ? ['browser'] : []),\n        ...config.resolve.conditions,\n      ];\n    },\n\n    configResolved(config) {\n      needHmr = config.command === 'serve' && config.mode !== 'production' && options.hot !== false;\n    },\n\n    resolveId(id) {\n      if (id === runtimePublicPath) return id;\n    },\n\n    load(id) {\n      if (id === runtimePublicPath) return runtimeCode;\n    },\n\n    async transform(source, id, transformOptions) {\n      const isSsr = transformOptions && transformOptions.ssr;\n      const currentFileExtension = getExtension(id);\n\n      const extensionsToWatch = options.extensions || [];\n      const allExtensions = extensionsToWatch.map((extension) =>\n        // An extension can be a string or a tuple [extension, options]\n        typeof extension === 'string' ? extension : extension[0],\n      );\n\n      if (!filter(id)) {\n        return null;\n      }\n\n      id = id.replace(/\\?.*$/, '');\n\n      if (!(/\\.[mc]?[tj]sx$/i.test(id) || allExtensions.includes(currentFileExtension))) {\n        return null;\n      }\n\n      const inNodeModules = /node_modules/.test(id);\n\n      let solidOptions: { generate: 'ssr' | 'dom'; hydratable: boolean };\n\n      if (options.ssr) {\n        if (isSsr) {\n          solidOptions = { generate: 'ssr', hydratable: true };\n        } else {\n          solidOptions = { generate: 'dom', hydratable: true };\n        }\n      } else {\n        solidOptions = { generate: 'dom', hydratable: false };\n      }\n\n      // We need to know if the current file extension has a typescript options tied to it\n      const shouldBeProcessedWithTypescript =\n        /\\.[mc]?tsx$/i.test(id) ||\n        extensionsToWatch.some((extension) => {\n          if (typeof extension === 'string') {\n            return extension.includes('tsx');\n          }\n\n          const [extensionName, extensionOptions] = extension;\n          if (extensionName !== currentFileExtension) return false;\n\n          return extensionOptions.typescript;\n        });\n      const plugins: NonNullable<NonNullable<babel.TransformOptions['parserOpts']>['plugins']> = [\n        'jsx',\n      ];\n\n      if (shouldBeProcessedWithTypescript) {\n        plugins.push('typescript');\n      }\n\n      const opts: babel.TransformOptions = {\n        root: projectRoot,\n        filename: id,\n        sourceFileName: id,\n        presets: [[solid, { ...solidOptions, ...(options.solid || {}) }]],\n        plugins: needHmr && !isSsr && !inNodeModules ? [[solidRefresh, { bundler: 'vite' }]] : [],\n        ast: false,\n        sourceMaps: true,\n        configFile: false,\n        babelrc: false,\n        parserOpts: {\n          plugins,\n        },\n      };\n\n      // Default value for babel user options\n      let babelUserOptions: babel.TransformOptions = {};\n\n      if (options.babel) {\n        if (typeof options.babel === 'function') {\n          const babelOptions = options.babel(source, id, isSsr);\n          babelUserOptions = babelOptions instanceof Promise ? await babelOptions : babelOptions;\n        } else {\n          babelUserOptions = options.babel;\n        }\n      }\n\n      const babelOptions = mergeAndConcat(babelUserOptions, opts) as babel.TransformOptions;\n\n      const { code, map } = await babel.transformAsync(source, babelOptions);\n\n      return { code, map };\n    },\n  };\n}\n\n/**\n * This basically normalize all aliases of the config into\n * the array format of the alias.\n *\n * eg: alias: { '@': 'src/' } => [{ find: '@', replacement: 'src/' }]\n */\nfunction normalizeAliases(alias: AliasOptions = []): Alias[] {\n  return Array.isArray(alias)\n    ? alias\n    : Object.entries(alias).map(([find, replacement]) => ({ find, replacement }));\n}\n"], "names": ["require", "createRequire", "import", "runtimePublicPath", "runtimeFile<PERSON>ath", "resolve", "runtimeCode", "readFileSync", "isVite6", "version", "split", "getExtension", "filename", "index", "lastIndexOf", "substring", "replace", "containsSolidField", "fields", "keys", "Object", "i", "length", "key", "getJestDomExport", "setupFiles", "some", "path", "test", "undefined", "find", "e", "solidPlugin", "options", "filter", "createFilter", "include", "exclude", "needHmr", "replace<PERSON>ev", "projectRoot", "process", "cwd", "isTestMode", "name", "enforce", "config", "userConfig", "command", "dev", "root", "mode", "alias", "normalizeAliases", "solidPkgsConfig", "crawlFrameworkPkgs", "viteUserConfig", "isBuild", "isFrameworkPkgByJson", "pkgJson", "exports", "nestedDeps", "userTest", "userSetupFiles", "environment", "ssr", "server", "deps", "external", "item", "toString", "browser", "enabled", "jestDomImport", "conditions", "dedupe", "replacement", "optimizeDeps", "configEnvironment", "opts", "defaultClientConditions", "defaultServerConditions", "consumer", "isSsrTargetWebworker", "configResolved", "hot", "resolveId", "id", "load", "transform", "source", "transformOptions", "isSsr", "currentFileExtension", "extensionsToWatch", "extensions", "allExtensions", "map", "extension", "includes", "inNodeModules", "solidOptions", "generate", "hydratable", "shouldBeProcessedWithTypescript", "extensionName", "extensionOptions", "typescript", "plugins", "push", "sourceFileName", "presets", "solid", "solidRefresh", "bundler", "ast", "sourceMaps", "configFile", "babelrc", "parserOpts", "babelUserOptions", "babel", "babelOptions", "Promise", "mergeAndConcat", "code", "transformAsync", "Array", "isArray", "entries"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,MAAMA,SAAO,GAAGC,sBAAa,CAACC,2PAAe,CAAC;AAE9C,MAAMC,iBAAiB,GAAG,iBAAiB;AAC3C,MAAMC,eAAe,GAAGJ,SAAO,CAACK,OAAO,CAAC,sCAAsC,CAAC;AAC/E,MAAMC,WAAW,GAAGC,eAAY,CAACH,eAAe,EAAE,OAAO,CAAC;AAE1D,MAAMI,OAAO,GAAG,CAACC,YAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;;AAE3C;;AAKA;;AA4IA,SAASC,YAAYA,CAACC,QAAgB,EAAU;AAC9C,EAAA,MAAMC,KAAK,GAAGD,QAAQ,CAACE,WAAW,CAAC,GAAG,CAAC;AACvC,EAAA,OAAOD,KAAK,GAAG,CAAC,GAAG,EAAE,GAAGD,QAAQ,CAACG,SAAS,CAACF,KAAK,CAAC,CAACG,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;AACxE;AACA,SAASC,kBAAkBA,CAACC,MAA2B,EAAE;AACvD,EAAA,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,MAAM,CAAC;AAChC,EAAA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;AACpC,IAAA,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;AACnB,IAAA,IAAIE,GAAG,KAAK,OAAO,EAAE,OAAO,IAAI;IAChC,IAAI,OAAOL,MAAM,CAACK,GAAG,CAAC,KAAK,QAAQ,IAAIL,MAAM,CAACK,GAAG,CAAC,IAAI,IAAI,IAAIN,kBAAkB,CAACC,MAAM,CAACK,GAAG,CAAC,CAAC,EAC3F,OAAO,IAAI;AACf;AACA,EAAA,OAAO,KAAK;AACd;AAEA,SAASC,gBAAgBA,CAACC,UAAoB,EAAE;EAC9C,OAAOA,UAAU,EAAEC,IAAI,CAAEC,IAAI,IAAK,UAAU,CAACC,IAAI,CAACD,IAAI,CAAC,CAAC,GACpDE,SAAS,GACT,CAAC,kCAAkC,EAAE,yCAAyC,CAAC,CAACC,IAAI,CACjFH,IAAI,IAAK;IACR,IAAI;AACF3B,MAAAA,SAAO,CAACK,OAAO,CAACsB,IAAI,CAAC;AACrB,MAAA,OAAO,IAAI;KACZ,CAAC,OAAOI,CAAC,EAAE;AACV,MAAA,OAAO,KAAK;AACd;AACF,GACF,CAAC;AACP;AAEe,SAASC,WAAWA,CAACC,OAAyB,GAAG,EAAE,EAAU;EAC1E,MAAMC,MAAM,GAAGC,iBAAY,CAACF,OAAO,CAACG,OAAO,EAAEH,OAAO,CAACI,OAAO,CAAC;EAE7D,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIC,UAAU,GAAG,KAAK;AACtB,EAAA,IAAIC,WAAW,GAAGC,OAAO,CAACC,GAAG,EAAE;EAC/B,IAAIC,UAAU,GAAG,KAAK;EAEtB,OAAO;AACLC,IAAAA,IAAI,EAAE,OAAO;AACbC,IAAAA,OAAO,EAAE,KAAK;IAEd,MAAMC,MAAMA,CAACC,UAAU,EAAE;AAAEC,MAAAA;AAAQ,KAAC,EAAE;AACpC;AACAT,MAAAA,UAAU,GAAGN,OAAO,CAACgB,GAAG,KAAK,IAAI,IAAKhB,OAAO,CAACgB,GAAG,KAAK,KAAK,IAAID,OAAO,KAAK,OAAQ;MACnFR,WAAW,GAAGO,UAAU,CAACG,IAAI;AAC7BP,MAAAA,UAAU,GAAGI,UAAU,CAACI,IAAI,KAAK,MAAM;MAEvC,IAAI,CAACJ,UAAU,CAAC1C,OAAO,EAAE0C,UAAU,CAAC1C,OAAO,GAAG,EAAE;AAChD0C,MAAAA,UAAU,CAAC1C,OAAO,CAAC+C,KAAK,GAAGC,gBAAgB,CAACN,UAAU,CAAC1C,OAAO,IAAI0C,UAAU,CAAC1C,OAAO,CAAC+C,KAAK,CAAC;AAE3F,MAAA,MAAME,eAAe,GAAG,MAAMC,yBAAkB,CAAC;AAC/CC,QAAAA,cAAc,EAAET,UAAU;AAC1BG,QAAAA,IAAI,EAAEV,WAAW,IAAIC,OAAO,CAACC,GAAG,EAAE;QAClCe,OAAO,EAAET,OAAO,KAAK,OAAO;QAC5BU,oBAAoBA,CAACC,OAAO,EAAE;UAC5B,OAAO1C,kBAAkB,CAAC0C,OAAO,CAACC,OAAO,IAAI,EAAE,CAAC;AAClD;AACF,OAAC,CAAC;;AAEF;AACA,MAAA,MAAMC,UAAU,GAAGtB,UAAU,GACzB,CAAC,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,eAAe,EAAE,YAAY,CAAC,GAC7E,EAAE;AAEN,MAAA,MAAMuB,QAAQ,GAAIf,UAAU,CAASnB,IAAI,IAAI,EAAE;MAC/C,MAAMA,IAAI,GAAG,EAAS;AACtB,MAAA,IAAImB,UAAU,CAACI,IAAI,KAAK,MAAM,EAAE;AAC9B;AACA,QAAA,MAAMY,cAAwB,GAC5B,OAAOD,QAAQ,CAACrC,UAAU,KAAK,QAAQ,GACnC,CAACqC,QAAQ,CAACrC,UAAU,CAAC,GACrBqC,QAAQ,CAACrC,UAAU,IAAI,EAAE;QAE/B,IAAI,CAACqC,QAAQ,CAACE,WAAW,IAAI,CAAC/B,OAAO,CAACgC,GAAG,EAAE;UACzCrC,IAAI,CAACoC,WAAW,GAAG,OAAO;AAC5B;QAEA,IACE,CAACF,QAAQ,CAACI,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEtC,IAAI,CAAEuC,IAAqB,IAC3D,UAAU,CAACzC,IAAI,CAACyC,IAAI,CAACC,QAAQ,EAAE,CACjC,CAAC,EACD;UACA1C,IAAI,CAACsC,MAAM,GAAG;AAAEC,YAAAA,IAAI,EAAE;cAAEC,QAAQ,EAAE,CAAC,UAAU;AAAE;WAAG;AACpD;AACA,QAAA,IAAI,CAACN,QAAQ,CAACS,OAAO,EAAEC,OAAO,EAAE;AAC9B;AACA;AACA,UAAA,MAAMC,aAAa,GAAGjD,gBAAgB,CAACuC,cAAc,CAAC;AACtD,UAAA,IAAIU,aAAa,EAAE;AACjB7C,YAAAA,IAAI,CAACH,UAAU,GAAG,CAACgD,aAAa,CAAC;AACnC;AACF;AACF;MAEA,OAAO;AACL;AACR;AACA;AACA;AACQ;AACApE,QAAAA,OAAO,EAAE;AACPqE,UAAAA,UAAU,EAAElE,OAAO,GACfqB,SAAS,GACT,CACE,OAAO,EACP,IAAIU,UAAU,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EACtC,IAAIQ,UAAU,CAACI,IAAI,KAAK,MAAM,IAAI,CAAClB,OAAO,CAACgC,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CACnE;AACLU,UAAAA,MAAM,EAAEd,UAAU;AAClBT,UAAAA,KAAK,EAAE,CAAC;AAAEtB,YAAAA,IAAI,EAAE,iBAAiB;AAAE8C,YAAAA,WAAW,EAAEzE;WAAmB;SACpE;AACD0E,QAAAA,YAAY,EAAE;UACZzC,OAAO,EAAE,CAAC,GAAGyB,UAAU,EAAE,GAAGP,eAAe,CAACuB,YAAY,CAACzC,OAAO,CAAC;AACjEC,UAAAA,OAAO,EAAEiB,eAAe,CAACuB,YAAY,CAACxC;SACvC;QACD4B,GAAG,EAAEX,eAAe,CAACW,GAAG;QACxB,IAAIrC,IAAI,CAACsC,MAAM,GAAG;AAAEtC,UAAAA;SAAM,GAAG,EAAE;OAChC;KACF;AAED;AACA,IAAA,MAAMkD,iBAAiBA,CAAClC,IAAI,EAAEE,MAAM,EAAEiC,IAAI,EAAE;AAC1CjC,MAAAA,MAAM,CAACzC,OAAO,KAAK,EAAE;AACrB;AACA,MAAA,IAAIyC,MAAM,CAACzC,OAAO,CAACqE,UAAU,IAAI,IAAI,EAAE;AACrC;QACA,MAAM;UAAEM,uBAAuB;AAAEC,UAAAA;AAAwB,SAAC,GAAG,MAAM,OAAO,MAAM,CAAC;AACjF,QAAA,IAAInC,MAAM,CAACoC,QAAQ,KAAK,QAAQ,IAAItC,IAAI,KAAK,QAAQ,IAAImC,IAAI,CAACI,oBAAoB,EAAE;UAClFrC,MAAM,CAACzC,OAAO,CAACqE,UAAU,GAAG,CAAC,GAAGM,uBAAuB,CAAC;AAC1D,SAAC,MAAM;UACLlC,MAAM,CAACzC,OAAO,CAACqE,UAAU,GAAG,CAAC,GAAGO,uBAAuB,CAAC;AAC1D;AACF;AACAnC,MAAAA,MAAM,CAACzC,OAAO,CAACqE,UAAU,GAAG,CAC1B,OAAO,EACP,IAAInC,UAAU,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EACtC,IAAII,UAAU,IAAI,CAACoC,IAAI,CAACI,oBAAoB,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAChE,GAAGrC,MAAM,CAACzC,OAAO,CAACqE,UAAU,CAC7B;KACF;IAEDU,cAAcA,CAACtC,MAAM,EAAE;AACrBR,MAAAA,OAAO,GAAGQ,MAAM,CAACE,OAAO,KAAK,OAAO,IAAIF,MAAM,CAACK,IAAI,KAAK,YAAY,IAAIlB,OAAO,CAACoD,GAAG,KAAK,KAAK;KAC9F;IAEDC,SAASA,CAACC,EAAE,EAAE;AACZ,MAAA,IAAIA,EAAE,KAAKpF,iBAAiB,EAAE,OAAOoF,EAAE;KACxC;IAEDC,IAAIA,CAACD,EAAE,EAAE;AACP,MAAA,IAAIA,EAAE,KAAKpF,iBAAiB,EAAE,OAAOG,WAAW;KACjD;AAED,IAAA,MAAMmF,SAASA,CAACC,MAAM,EAAEH,EAAE,EAAEI,gBAAgB,EAAE;AAC5C,MAAA,MAAMC,KAAK,GAAGD,gBAAgB,IAAIA,gBAAgB,CAAC1B,GAAG;AACtD,MAAA,MAAM4B,oBAAoB,GAAGlF,YAAY,CAAC4E,EAAE,CAAC;AAE7C,MAAA,MAAMO,iBAAiB,GAAG7D,OAAO,CAAC8D,UAAU,IAAI,EAAE;AAClD,MAAA,MAAMC,aAAa,GAAGF,iBAAiB,CAACG,GAAG,CAAEC,SAAS;AACpD;MACA,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGA,SAAS,CAAC,CAAC,CACzD,CAAC;AAED,MAAA,IAAI,CAAChE,MAAM,CAACqD,EAAE,CAAC,EAAE;AACf,QAAA,OAAO,IAAI;AACb;MAEAA,EAAE,GAAGA,EAAE,CAACvE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;AAE5B,MAAA,IAAI,EAAE,iBAAiB,CAACY,IAAI,CAAC2D,EAAE,CAAC,IAAIS,aAAa,CAACG,QAAQ,CAACN,oBAAoB,CAAC,CAAC,EAAE;AACjF,QAAA,OAAO,IAAI;AACb;AAEA,MAAA,MAAMO,aAAa,GAAG,cAAc,CAACxE,IAAI,CAAC2D,EAAE,CAAC;AAE7C,MAAA,IAAIc,YAA8D;MAElE,IAAIpE,OAAO,CAACgC,GAAG,EAAE;AACf,QAAA,IAAI2B,KAAK,EAAE;AACTS,UAAAA,YAAY,GAAG;AAAEC,YAAAA,QAAQ,EAAE,KAAK;AAAEC,YAAAA,UAAU,EAAE;WAAM;AACtD,SAAC,MAAM;AACLF,UAAAA,YAAY,GAAG;AAAEC,YAAAA,QAAQ,EAAE,KAAK;AAAEC,YAAAA,UAAU,EAAE;WAAM;AACtD;AACF,OAAC,MAAM;AACLF,QAAAA,YAAY,GAAG;AAAEC,UAAAA,QAAQ,EAAE,KAAK;AAAEC,UAAAA,UAAU,EAAE;SAAO;AACvD;;AAEA;AACA,MAAA,MAAMC,+BAA+B,GACnC,cAAc,CAAC5E,IAAI,CAAC2D,EAAE,CAAC,IACvBO,iBAAiB,CAACpE,IAAI,CAAEwE,SAAS,IAAK;AACpC,QAAA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;AACjC,UAAA,OAAOA,SAAS,CAACC,QAAQ,CAAC,KAAK,CAAC;AAClC;AAEA,QAAA,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,SAAS;AACnD,QAAA,IAAIO,aAAa,KAAKZ,oBAAoB,EAAE,OAAO,KAAK;QAExD,OAAOa,gBAAgB,CAACC,UAAU;AACpC,OAAC,CAAC;AACJ,MAAA,MAAMC,OAAkF,GAAG,CACzF,KAAK,CACN;AAED,MAAA,IAAIJ,+BAA+B,EAAE;AACnCI,QAAAA,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;AAC5B;AAEA,MAAA,MAAM9B,IAA4B,GAAG;AACnC7B,QAAAA,IAAI,EAAEV,WAAW;AACjB5B,QAAAA,QAAQ,EAAE2E,EAAE;AACZuB,QAAAA,cAAc,EAAEvB,EAAE;AAClBwB,QAAAA,OAAO,EAAE,CAAC,CAACC,KAAK,EAAE;AAAE,UAAA,GAAGX,YAAY;AAAE,UAAA,IAAIpE,OAAO,CAAC+E,KAAK,IAAI,EAAE;AAAE,SAAC,CAAC,CAAC;AACjEJ,QAAAA,OAAO,EAAEtE,OAAO,IAAI,CAACsD,KAAK,IAAI,CAACQ,aAAa,GAAG,CAAC,CAACa,YAAY,EAAE;AAAEC,UAAAA,OAAO,EAAE;SAAQ,CAAC,CAAC,GAAG,EAAE;AACzFC,QAAAA,GAAG,EAAE,KAAK;AACVC,QAAAA,UAAU,EAAE,IAAI;AAChBC,QAAAA,UAAU,EAAE,KAAK;AACjBC,QAAAA,OAAO,EAAE,KAAK;AACdC,QAAAA,UAAU,EAAE;AACVX,UAAAA;AACF;OACD;;AAED;MACA,IAAIY,gBAAwC,GAAG,EAAE;MAEjD,IAAIvF,OAAO,CAACwF,KAAK,EAAE;AACjB,QAAA,IAAI,OAAOxF,OAAO,CAACwF,KAAK,KAAK,UAAU,EAAE;UACvC,MAAMC,YAAY,GAAGzF,OAAO,CAACwF,KAAK,CAAC/B,MAAM,EAAEH,EAAE,EAAEK,KAAK,CAAC;UACrD4B,gBAAgB,GAAGE,YAAY,YAAYC,OAAO,GAAG,MAAMD,YAAY,GAAGA,YAAY;AACxF,SAAC,MAAM;UACLF,gBAAgB,GAAGvF,OAAO,CAACwF,KAAK;AAClC;AACF;AAEA,MAAA,MAAMC,YAAY,GAAGE,4BAAc,CAACJ,gBAAgB,EAAEzC,IAAI,CAA2B;MAErF,MAAM;QAAE8C,IAAI;AAAE5B,QAAAA;OAAK,GAAG,MAAMwB,gBAAK,CAACK,cAAc,CAACpC,MAAM,EAAEgC,YAAY,CAAC;MAEtE,OAAO;QAAEG,IAAI;AAAE5B,QAAAA;OAAK;AACtB;GACD;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5C,gBAAgBA,CAACD,KAAmB,GAAG,EAAE,EAAW;EAC3D,OAAO2E,KAAK,CAACC,OAAO,CAAC5E,KAAK,CAAC,GACvBA,KAAK,GACLhC,MAAM,CAAC6G,OAAO,CAAC7E,KAAK,CAAC,CAAC6C,GAAG,CAAC,CAAC,CAACnE,IAAI,EAAE8C,WAAW,CAAC,MAAM;IAAE9C,IAAI;AAAE8C,IAAAA;AAAY,GAAC,CAAC,CAAC;AACjF;;;;"}